buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "2.0.21"
        androidXBrowser = "1.3.0"

        playServicesLocationVersion = "21.0.1"
    }
    repositories {
        google()
        mavenCentral()        
    }

    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("com.google.gms:google-services:4.4.0")
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath 'com.google.firebase:perf-plugin:1.4.2'

    }
}

 
project.ext.react = [
        nodeExecutableAndArgs: ["~/.nvm/versions/node/v21.7.3/bin/node"]
]

apply plugin: "com.facebook.react.rootproject"
