
//  (C) Copyright <PERSON>, <PERSON>, <PERSON><PERSON>, 
//      <PERSON> and <PERSON> 2000, 2010. 
//  (C) Copyright <PERSON>, <PERSON> and Adobe Systems Inc 2001

//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).
//
//  See http://www.boost.org/libs/type_traits for most recent version including documentation.

#ifndef BOOST_TT_IS_REFERENCE_HPP_INCLUDED
#define BOOST_TT_IS_REFERENCE_HPP_INCLUDED

#include <boost/type_traits/is_lvalue_reference.hpp>
#include <boost/type_traits/is_rvalue_reference.hpp>

namespace boost {

template <class T> struct is_reference 
   : public 
   integral_constant<
      bool, 
      ::boost::is_lvalue_reference<T>::value || ::boost::is_rvalue_reference<T>::value>
{};

} // namespace boost

#endif // BOOST_TT_IS_REFERENCE_HPP_INCLUDED

