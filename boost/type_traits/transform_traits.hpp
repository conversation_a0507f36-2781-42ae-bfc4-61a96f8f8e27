//  (C) Copyright <PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON> 2000.
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).
//
//  See http://www.boost.org/libs/type_traits for most recent version including documentation.
//
//  defines traits classes for transforming one type to another:
//  remove_reference, add_reference, remove_bounds, remove_pointer.
//

#ifndef BOOST_TT_TRANSFORM_TRAITS_HPP_INCLUDED
#define BOOST_TT_TRANSFORM_TRAITS_HPP_INCLUDED

#include <boost/type_traits/add_pointer.hpp>
#include <boost/type_traits/add_reference.hpp>
#include <boost/type_traits/remove_bounds.hpp>
#include <boost/type_traits/remove_pointer.hpp>
#include <boost/type_traits/remove_reference.hpp>

#endif // BOOST_TT_TRANSFORM_TRAITS_HPP_INCLUDED
