# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_PUNCTUATION_HPP
# define BOOST_PREPROCESSOR_PUNCTUATION_HPP
#
# include <boost/preprocessor/punctuation/comma.hpp>
# include <boost/preprocessor/punctuation/comma_if.hpp>
# include <boost/preprocessor/punctuation/is_begin_parens.hpp>
# include <boost/preprocessor/punctuation/paren.hpp>
# include <boost/preprocessor/punctuation/paren_if.hpp>
# include <boost/preprocessor/punctuation/remove_parens.hpp>
#
# endif
