# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SEQ_PUSH_BACK_HPP
# define BOOST_PREPROCESSOR_SEQ_PUSH_BACK_HPP
#
# /* BOOST_PP_SEQ_PUSH_BACK */
#
# define BOOST_PP_SEQ_PUSH_BACK(seq, elem) seq(elem)
#
# endif
