# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_LIST_DETAIL_EDG_FOLD_RIGHT_HPP
# define BOOST_PREPROCESSOR_LIST_DETAIL_EDG_FOLD_RIGHT_HPP
#
# include <boost/preprocessor/config/config.hpp>
#
# if ~BOOST_PP_CONFIG_FLAGS() & BOOST_PP_CONFIG_STRICT()
#
# include <boost/preprocessor/control/iif.hpp>
# include <boost/preprocessor/list/adt.hpp>
# include <boost/preprocessor/tuple/eat.hpp>
#
# define BOOST_PP_LIST_FOLD_RIGHT_1(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_1_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_2(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_2_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_3(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_3_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_4(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_4_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_5(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_5_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_6(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_6_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_7(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_7_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_8(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_8_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_9(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_9_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_10(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_10_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_11(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_11_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_12(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_12_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_13(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_13_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_14(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_14_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_15(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_15_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_16(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_16_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_17(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_17_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_18(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_18_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_19(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_19_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_20(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_20_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_21(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_21_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_22(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_22_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_23(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_23_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_24(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_24_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_25(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_25_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_26(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_26_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_27(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_27_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_28(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_28_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_29(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_29_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_30(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_30_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_31(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_31_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_32(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_32_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_33(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_33_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_34(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_34_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_35(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_35_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_36(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_36_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_37(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_37_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_38(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_38_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_39(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_39_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_40(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_40_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_41(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_41_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_42(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_42_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_43(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_43_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_44(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_44_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_45(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_45_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_46(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_46_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_47(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_47_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_48(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_48_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_49(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_49_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_50(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_50_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_51(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_51_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_52(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_52_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_53(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_53_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_54(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_54_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_55(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_55_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_56(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_56_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_57(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_57_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_58(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_58_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_59(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_59_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_60(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_60_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_61(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_61_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_62(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_62_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_63(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_63_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_64(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_64_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_65(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_65_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_66(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_66_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_67(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_67_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_68(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_68_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_69(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_69_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_70(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_70_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_71(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_71_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_72(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_72_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_73(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_73_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_74(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_74_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_75(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_75_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_76(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_76_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_77(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_77_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_78(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_78_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_79(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_79_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_80(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_80_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_81(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_81_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_82(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_82_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_83(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_83_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_84(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_84_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_85(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_85_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_86(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_86_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_87(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_87_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_88(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_88_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_89(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_89_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_90(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_90_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_91(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_91_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_92(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_92_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_93(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_93_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_94(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_94_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_95(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_95_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_96(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_96_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_97(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_97_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_98(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_98_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_99(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_99_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_100(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_100_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_101(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_101_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_102(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_102_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_103(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_103_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_104(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_104_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_105(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_105_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_106(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_106_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_107(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_107_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_108(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_108_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_109(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_109_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_110(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_110_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_111(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_111_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_112(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_112_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_113(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_113_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_114(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_114_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_115(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_115_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_116(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_116_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_117(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_117_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_118(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_118_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_119(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_119_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_120(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_120_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_121(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_121_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_122(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_122_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_123(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_123_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_124(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_124_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_125(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_125_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_126(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_126_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_127(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_127_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_128(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_128_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_129(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_129_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_130(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_130_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_131(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_131_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_132(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_132_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_133(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_133_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_134(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_134_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_135(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_135_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_136(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_136_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_137(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_137_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_138(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_138_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_139(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_139_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_140(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_140_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_141(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_141_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_142(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_142_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_143(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_143_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_144(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_144_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_145(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_145_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_146(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_146_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_147(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_147_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_148(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_148_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_149(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_149_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_150(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_150_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_151(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_151_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_152(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_152_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_153(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_153_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_154(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_154_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_155(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_155_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_156(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_156_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_157(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_157_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_158(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_158_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_159(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_159_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_160(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_160_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_161(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_161_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_162(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_162_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_163(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_163_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_164(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_164_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_165(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_165_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_166(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_166_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_167(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_167_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_168(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_168_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_169(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_169_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_170(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_170_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_171(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_171_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_172(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_172_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_173(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_173_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_174(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_174_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_175(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_175_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_176(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_176_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_177(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_177_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_178(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_178_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_179(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_179_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_180(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_180_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_181(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_181_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_182(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_182_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_183(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_183_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_184(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_184_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_185(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_185_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_186(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_186_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_187(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_187_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_188(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_188_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_189(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_189_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_190(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_190_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_191(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_191_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_192(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_192_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_193(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_193_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_194(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_194_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_195(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_195_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_196(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_196_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_197(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_197_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_198(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_198_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_199(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_199_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_200(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_200_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_201(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_201_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_202(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_202_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_203(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_203_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_204(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_204_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_205(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_205_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_206(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_206_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_207(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_207_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_208(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_208_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_209(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_209_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_210(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_210_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_211(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_211_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_212(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_212_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_213(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_213_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_214(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_214_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_215(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_215_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_216(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_216_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_217(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_217_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_218(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_218_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_219(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_219_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_220(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_220_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_221(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_221_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_222(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_222_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_223(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_223_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_224(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_224_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_225(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_225_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_226(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_226_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_227(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_227_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_228(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_228_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_229(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_229_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_230(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_230_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_231(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_231_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_232(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_232_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_233(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_233_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_234(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_234_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_235(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_235_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_236(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_236_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_237(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_237_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_238(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_238_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_239(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_239_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_240(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_240_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_241(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_241_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_242(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_242_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_243(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_243_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_244(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_244_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_245(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_245_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_246(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_246_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_247(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_247_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_248(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_248_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_249(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_249_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_250(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_250_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_251(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_251_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_252(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_252_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_253(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_253_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_254(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_254_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_255(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_255_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_256(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_256_D(o, s, l)
#
# define BOOST_PP_LIST_FOLD_RIGHT_1_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(2, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_2, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_2_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(3, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_3, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_3_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(4, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_4, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_4_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(5, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_5, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_5_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(6, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_6, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_6_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(7, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_7, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_7_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(8, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_8, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_8_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(9, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_9, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_9_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(10, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_10, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_10_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(11, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_11, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_11_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(12, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_12, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_12_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(13, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_13, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_13_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(14, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_14, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_14_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(15, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_15, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_15_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(16, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_16, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_16_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(17, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_17, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_17_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(18, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_18, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_18_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(19, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_19, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_19_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(20, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_20, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_20_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(21, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_21, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_21_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(22, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_22, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_22_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(23, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_23, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_23_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(24, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_24, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_24_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(25, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_25, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_25_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(26, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_26, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_26_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(27, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_27, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_27_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(28, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_28, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_28_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(29, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_29, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_29_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(30, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_30, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_30_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(31, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_31, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_31_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(32, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_32, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_32_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(33, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_33, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_33_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(34, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_34, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_34_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(35, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_35, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_35_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(36, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_36, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_36_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(37, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_37, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_37_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(38, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_38, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_38_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(39, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_39, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_39_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(40, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_40, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_40_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(41, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_41, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_41_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(42, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_42, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_42_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(43, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_43, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_43_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(44, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_44, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_44_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(45, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_45, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_45_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(46, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_46, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_46_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(47, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_47, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_47_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(48, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_48, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_48_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(49, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_49, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_49_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(50, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_50, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_50_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(51, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_51, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_51_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(52, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_52, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_52_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(53, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_53, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_53_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(54, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_54, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_54_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(55, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_55, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_55_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(56, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_56, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_56_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(57, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_57, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_57_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(58, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_58, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_58_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(59, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_59, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_59_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(60, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_60, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_60_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(61, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_61, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_61_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(62, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_62, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_62_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(63, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_63, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_63_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(64, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_64, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_64_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(65, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_65, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_65_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(66, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_66, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_66_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(67, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_67, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_67_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(68, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_68, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_68_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(69, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_69, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_69_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(70, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_70, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_70_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(71, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_71, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_71_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(72, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_72, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_72_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(73, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_73, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_73_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(74, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_74, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_74_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(75, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_75, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_75_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(76, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_76, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_76_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(77, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_77, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_77_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(78, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_78, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_78_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(79, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_79, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_79_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(80, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_80, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_80_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(81, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_81, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_81_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(82, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_82, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_82_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(83, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_83, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_83_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(84, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_84, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_84_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(85, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_85, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_85_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(86, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_86, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_86_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(87, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_87, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_87_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(88, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_88, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_88_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(89, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_89, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_89_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(90, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_90, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_90_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(91, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_91, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_91_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(92, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_92, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_92_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(93, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_93, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_93_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(94, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_94, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_94_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(95, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_95, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_95_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(96, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_96, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_96_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(97, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_97, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_97_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(98, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_98, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_98_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(99, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_99, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_99_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(100, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_100, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_100_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(101, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_101, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_101_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(102, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_102, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_102_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(103, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_103, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_103_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(104, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_104, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_104_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(105, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_105, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_105_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(106, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_106, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_106_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(107, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_107, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_107_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(108, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_108, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_108_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(109, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_109, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_109_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(110, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_110, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_110_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(111, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_111, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_111_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(112, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_112, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_112_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(113, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_113, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_113_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(114, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_114, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_114_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(115, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_115, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_115_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(116, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_116, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_116_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(117, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_117, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_117_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(118, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_118, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_118_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(119, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_119, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_119_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(120, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_120, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_120_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(121, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_121, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_121_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(122, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_122, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_122_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(123, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_123, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_123_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(124, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_124, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_124_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(125, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_125, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_125_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(126, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_126, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_126_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(127, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_127, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_127_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(128, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_128, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_128_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(129, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_129, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_129_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(130, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_130, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_130_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(131, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_131, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_131_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(132, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_132, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_132_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(133, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_133, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_133_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(134, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_134, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_134_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(135, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_135, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_135_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(136, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_136, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_136_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(137, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_137, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_137_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(138, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_138, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_138_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(139, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_139, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_139_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(140, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_140, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_140_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(141, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_141, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_141_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(142, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_142, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_142_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(143, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_143, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_143_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(144, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_144, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_144_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(145, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_145, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_145_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(146, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_146, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_146_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(147, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_147, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_147_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(148, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_148, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_148_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(149, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_149, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_149_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(150, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_150, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_150_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(151, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_151, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_151_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(152, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_152, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_152_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(153, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_153, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_153_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(154, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_154, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_154_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(155, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_155, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_155_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(156, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_156, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_156_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(157, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_157, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_157_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(158, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_158, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_158_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(159, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_159, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_159_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(160, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_160, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_160_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(161, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_161, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_161_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(162, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_162, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_162_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(163, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_163, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_163_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(164, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_164, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_164_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(165, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_165, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_165_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(166, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_166, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_166_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(167, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_167, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_167_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(168, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_168, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_168_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(169, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_169, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_169_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(170, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_170, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_170_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(171, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_171, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_171_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(172, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_172, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_172_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(173, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_173, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_173_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(174, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_174, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_174_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(175, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_175, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_175_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(176, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_176, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_176_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(177, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_177, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_177_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(178, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_178, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_178_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(179, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_179, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_179_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(180, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_180, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_180_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(181, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_181, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_181_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(182, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_182, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_182_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(183, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_183, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_183_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(184, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_184, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_184_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(185, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_185, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_185_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(186, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_186, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_186_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(187, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_187, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_187_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(188, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_188, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_188_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(189, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_189, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_189_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(190, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_190, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_190_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(191, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_191, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_191_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(192, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_192, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_192_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(193, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_193, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_193_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(194, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_194, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_194_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(195, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_195, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_195_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(196, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_196, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_196_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(197, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_197, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_197_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(198, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_198, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_198_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(199, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_199, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_199_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(200, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_200, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_200_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(201, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_201, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_201_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(202, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_202, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_202_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(203, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_203, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_203_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(204, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_204, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_204_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(205, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_205, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_205_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(206, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_206, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_206_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(207, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_207, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_207_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(208, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_208, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_208_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(209, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_209, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_209_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(210, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_210, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_210_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(211, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_211, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_211_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(212, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_212, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_212_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(213, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_213, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_213_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(214, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_214, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_214_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(215, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_215, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_215_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(216, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_216, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_216_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(217, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_217, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_217_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(218, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_218, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_218_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(219, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_219, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_219_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(220, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_220, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_220_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(221, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_221, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_221_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(222, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_222, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_222_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(223, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_223, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_223_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(224, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_224, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_224_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(225, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_225, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_225_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(226, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_226, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_226_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(227, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_227, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_227_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(228, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_228, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_228_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(229, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_229, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_229_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(230, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_230, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_230_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(231, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_231, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_231_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(232, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_232, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_232_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(233, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_233, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_233_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(234, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_234, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_234_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(235, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_235, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_235_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(236, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_236, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_236_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(237, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_237, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_237_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(238, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_238, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_238_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(239, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_239, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_239_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(240, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_240, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_240_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(241, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_241, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_241_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(242, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_242, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_242_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(243, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_243, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_243_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(244, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_244, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_244_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(245, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_245, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_245_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(246, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_246, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_246_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(247, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_247, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_247_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(248, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_248, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_248_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(249, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_249, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_249_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(250, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_250, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_250_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(251, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_251, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_251_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(252, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_252, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_252_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(253, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_253, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_253_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(254, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_254, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_254_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(255, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_255, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_255_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(256, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_256, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_256_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(257, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_257, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
#
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_NIL 1
#
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_1(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_2(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_3(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_4(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_5(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_6(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_7(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_8(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_9(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_10(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_11(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_12(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_13(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_14(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_15(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_16(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_17(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_18(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_19(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_20(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_21(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_22(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_23(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_24(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_25(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_26(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_27(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_28(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_29(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_30(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_31(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_32(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_33(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_34(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_35(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_36(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_37(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_38(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_39(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_40(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_41(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_42(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_43(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_44(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_45(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_46(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_47(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_48(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_49(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_50(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_51(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_52(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_53(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_54(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_55(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_56(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_57(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_58(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_59(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_60(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_61(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_62(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_63(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_64(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_65(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_66(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_67(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_68(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_69(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_70(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_71(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_72(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_73(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_74(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_75(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_76(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_77(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_78(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_79(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_80(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_81(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_82(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_83(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_84(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_85(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_86(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_87(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_88(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_89(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_90(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_91(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_92(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_93(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_94(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_95(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_96(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_97(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_98(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_99(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_100(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_101(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_102(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_103(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_104(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_105(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_106(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_107(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_108(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_109(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_110(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_111(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_112(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_113(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_114(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_115(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_116(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_117(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_118(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_119(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_120(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_121(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_122(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_123(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_124(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_125(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_126(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_127(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_128(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_129(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_130(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_131(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_132(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_133(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_134(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_135(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_136(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_137(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_138(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_139(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_140(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_141(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_142(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_143(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_144(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_145(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_146(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_147(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_148(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_149(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_150(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_151(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_152(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_153(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_154(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_155(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_156(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_157(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_158(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_159(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_160(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_161(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_162(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_163(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_164(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_165(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_166(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_167(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_168(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_169(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_170(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_171(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_172(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_173(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_174(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_175(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_176(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_177(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_178(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_179(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_180(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_181(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_182(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_183(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_184(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_185(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_186(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_187(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_188(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_189(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_190(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_191(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_192(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_193(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_194(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_195(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_196(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_197(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_198(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_199(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_200(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_201(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_202(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_203(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_204(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_205(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_206(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_207(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_208(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_209(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_210(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_211(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_212(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_213(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_214(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_215(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_216(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_217(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_218(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_219(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_220(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_221(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_222(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_223(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_224(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_225(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_226(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_227(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_228(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_229(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_230(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_231(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_232(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_233(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_234(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_235(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_236(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_237(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_238(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_239(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_240(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_241(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_242(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_243(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_244(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_245(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_246(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_247(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_248(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_249(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_250(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_251(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_252(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_253(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_254(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_255(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_256(o, s, l) 0
#
# else
#
# include <boost/preprocessor/control/iif.hpp>
# include <boost/preprocessor/list/adt.hpp>
# include <boost/preprocessor/tuple/eat.hpp>
# include <boost/preprocessor/config/limits.hpp>
#
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_NIL 1
#
# if BOOST_PP_LIMIT_WHILE == 256
# include <boost/preprocessor/list/detail/edg/limits/fold_right_256.hpp>
# elif BOOST_PP_LIMIT_WHILE == 512
# include <boost/preprocessor/list/detail/edg/limits/fold_right_256.hpp>
# include <boost/preprocessor/list/detail/edg/limits/fold_right_512.hpp>
# elif BOOST_PP_LIMIT_WHILE == 1024
# include <boost/preprocessor/list/detail/edg/limits/fold_right_256.hpp>
# include <boost/preprocessor/list/detail/edg/limits/fold_right_512.hpp>
# include <boost/preprocessor/list/detail/edg/limits/fold_right_1024.hpp>
# else
# error Incorrect value for the BOOST_PP_LIMIT_WHILE limit
# endif
#
# endif
#
# endif
