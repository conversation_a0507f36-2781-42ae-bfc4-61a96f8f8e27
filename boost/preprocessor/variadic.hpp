# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2011.                                    *
#  *     (C) Copyright <PERSON> 2011.                                  *
#  *     Distributed under the Boost Software License, Version 1.0. (See      *
#  *     accompanying file LICENSE_1_0.txt or copy at                         *
#  *     http://www.boost.org/LICENSE_1_0.txt)                                *
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_VARIADIC_HPP
# define BOOST_PREPROCESSOR_VARIADIC_HPP
#
# include <boost/preprocessor/variadic/elem.hpp>
# include <boost/preprocessor/variadic/has_opt.hpp>
# include <boost/preprocessor/variadic/size.hpp>
# include <boost/preprocessor/variadic/to_array.hpp>
# include <boost/preprocessor/variadic/to_list.hpp>
# include <boost/preprocessor/variadic/to_seq.hpp>
# include <boost/preprocessor/variadic/to_tuple.hpp>
#
# endif
