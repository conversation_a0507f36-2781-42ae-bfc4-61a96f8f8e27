/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <folly/ssl/PasswordCollector.h>

namespace folly {

class PasswordInFile : public ssl::PasswordCollector {
 public:
  explicit PasswordInFile(const std::string& file);
  ~PasswordInFile() override;

  void getPassword(std::string& password, int /* size */) const override {
    password = password_;
  }

  const char* getPasswordStr() const { return password_.c_str(); }

  const std::string& describe() const override { return fileName_; }

 protected:
  std::string fileName_;
  std::string password_;
};

} // namespace folly
