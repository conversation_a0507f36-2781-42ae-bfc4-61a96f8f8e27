/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <glog/logging.h>

#include <folly/experimental/io/FsUtil.h>
#include <folly/testing/TestUtil.h>

namespace folly {
namespace test {

class TempFileUtil {
 public:
  // Returns a temporary file that is NOT kept open
  // but is deleted on destruction
  // Generate random-looking but reproduceable data.
  static TemporaryFile getTempFile(size_t size);

 private:
  TempFileUtil() = delete;
};

} // namespace test
} // namespace folly
