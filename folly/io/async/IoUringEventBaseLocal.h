/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <folly/experimental/io/IoUringBackend.h>
#include <folly/experimental/io/Liburing.h>
#include <folly/io/async/EventBase.h>

namespace folly {

#if FOLLY_HAS_LIBURING

class IoUringEventBaseLocal {
 public:
  static void attach(
      EventBase* evb,
      IoUringBackend::Options const& options,
      bool use_eventfd = true);
  static IoUringBackend* try_get(EventBase* evb);
};

#endif

} // namespace folly
