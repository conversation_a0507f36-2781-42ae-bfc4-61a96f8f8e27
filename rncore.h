
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniH.js
 */

#pragma once

#include <ReactCommon/JavaTurboModule.h>
#include <ReactCommon/TurboModule.h>
#include <jsi/jsi.h>

namespace facebook::react {

/**
 * JNI C++ class for module 'NativeAccessibilityInfo'
 */
class JSI_EXPORT NativeAccessibilityInfoSpecJSI : public JavaTurboModule {
public:
  NativeAccessibilityInfoSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeAccessibilityManager'
 */
class JSI_EXPORT NativeAccessibilityManagerSpecJSI : public JavaTurboModule {
public:
  NativeAccessibilityManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeActionSheetManager'
 */
class JSI_EXPORT NativeActionSheetManagerSpecJSI : public JavaTurboModule {
public:
  NativeActionSheetManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeAlertManager'
 */
class JSI_EXPORT NativeAlertManagerSpecJSI : public JavaTurboModule {
public:
  NativeAlertManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeAnimatedModule'
 */
class JSI_EXPORT NativeAnimatedModuleSpecJSI : public JavaTurboModule {
public:
  NativeAnimatedModuleSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeAnimatedTurboModule'
 */
class JSI_EXPORT NativeAnimatedTurboModuleSpecJSI : public JavaTurboModule {
public:
  NativeAnimatedTurboModuleSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeAppState'
 */
class JSI_EXPORT NativeAppStateSpecJSI : public JavaTurboModule {
public:
  NativeAppStateSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeAppearance'
 */
class JSI_EXPORT NativeAppearanceSpecJSI : public JavaTurboModule {
public:
  NativeAppearanceSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeBlobModule'
 */
class JSI_EXPORT NativeBlobModuleSpecJSI : public JavaTurboModule {
public:
  NativeBlobModuleSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeBugReporting'
 */
class JSI_EXPORT NativeBugReportingSpecJSI : public JavaTurboModule {
public:
  NativeBugReportingSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeClipboard'
 */
class JSI_EXPORT NativeClipboardSpecJSI : public JavaTurboModule {
public:
  NativeClipboardSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeDevLoadingView'
 */
class JSI_EXPORT NativeDevLoadingViewSpecJSI : public JavaTurboModule {
public:
  NativeDevLoadingViewSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeDevMenu'
 */
class JSI_EXPORT NativeDevMenuSpecJSI : public JavaTurboModule {
public:
  NativeDevMenuSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeDevSettings'
 */
class JSI_EXPORT NativeDevSettingsSpecJSI : public JavaTurboModule {
public:
  NativeDevSettingsSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeDeviceEventManager'
 */
class JSI_EXPORT NativeDeviceEventManagerSpecJSI : public JavaTurboModule {
public:
  NativeDeviceEventManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeDeviceInfo'
 */
class JSI_EXPORT NativeDeviceInfoSpecJSI : public JavaTurboModule {
public:
  NativeDeviceInfoSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeDialogManagerAndroid'
 */
class JSI_EXPORT NativeDialogManagerAndroidSpecJSI : public JavaTurboModule {
public:
  NativeDialogManagerAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeExceptionsManager'
 */
class JSI_EXPORT NativeExceptionsManagerSpecJSI : public JavaTurboModule {
public:
  NativeExceptionsManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeFileReaderModule'
 */
class JSI_EXPORT NativeFileReaderModuleSpecJSI : public JavaTurboModule {
public:
  NativeFileReaderModuleSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeFrameRateLogger'
 */
class JSI_EXPORT NativeFrameRateLoggerSpecJSI : public JavaTurboModule {
public:
  NativeFrameRateLoggerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeHeadlessJsTaskSupport'
 */
class JSI_EXPORT NativeHeadlessJsTaskSupportSpecJSI : public JavaTurboModule {
public:
  NativeHeadlessJsTaskSupportSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeI18nManager'
 */
class JSI_EXPORT NativeI18nManagerSpecJSI : public JavaTurboModule {
public:
  NativeI18nManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeImageEditor'
 */
class JSI_EXPORT NativeImageEditorSpecJSI : public JavaTurboModule {
public:
  NativeImageEditorSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeImageLoaderAndroid'
 */
class JSI_EXPORT NativeImageLoaderAndroidSpecJSI : public JavaTurboModule {
public:
  NativeImageLoaderAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeImageStoreAndroid'
 */
class JSI_EXPORT NativeImageStoreAndroidSpecJSI : public JavaTurboModule {
public:
  NativeImageStoreAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeIntentAndroid'
 */
class JSI_EXPORT NativeIntentAndroidSpecJSI : public JavaTurboModule {
public:
  NativeIntentAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeJSCHeapCapture'
 */
class JSI_EXPORT NativeJSCHeapCaptureSpecJSI : public JavaTurboModule {
public:
  NativeJSCHeapCaptureSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeKeyboardObserver'
 */
class JSI_EXPORT NativeKeyboardObserverSpecJSI : public JavaTurboModule {
public:
  NativeKeyboardObserverSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeLinkingManager'
 */
class JSI_EXPORT NativeLinkingManagerSpecJSI : public JavaTurboModule {
public:
  NativeLinkingManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeLogBox'
 */
class JSI_EXPORT NativeLogBoxSpecJSI : public JavaTurboModule {
public:
  NativeLogBoxSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeModalManager'
 */
class JSI_EXPORT NativeModalManagerSpecJSI : public JavaTurboModule {
public:
  NativeModalManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeNetworkingAndroid'
 */
class JSI_EXPORT NativeNetworkingAndroidSpecJSI : public JavaTurboModule {
public:
  NativeNetworkingAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativePermissionsAndroid'
 */
class JSI_EXPORT NativePermissionsAndroidSpecJSI : public JavaTurboModule {
public:
  NativePermissionsAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativePlatformConstantsAndroid'
 */
class JSI_EXPORT NativePlatformConstantsAndroidSpecJSI : public JavaTurboModule {
public:
  NativePlatformConstantsAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeReactDevToolsRuntimeSettingsModule'
 */
class JSI_EXPORT NativeReactDevToolsRuntimeSettingsModuleSpecJSI : public JavaTurboModule {
public:
  NativeReactDevToolsRuntimeSettingsModuleSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeReactDevToolsSettingsManager'
 */
class JSI_EXPORT NativeReactDevToolsSettingsManagerSpecJSI : public JavaTurboModule {
public:
  NativeReactDevToolsSettingsManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeRedBox'
 */
class JSI_EXPORT NativeRedBoxSpecJSI : public JavaTurboModule {
public:
  NativeRedBoxSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeSegmentFetcher'
 */
class JSI_EXPORT NativeSegmentFetcherSpecJSI : public JavaTurboModule {
public:
  NativeSegmentFetcherSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeSettingsManager'
 */
class JSI_EXPORT NativeSettingsManagerSpecJSI : public JavaTurboModule {
public:
  NativeSettingsManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeShareModule'
 */
class JSI_EXPORT NativeShareModuleSpecJSI : public JavaTurboModule {
public:
  NativeShareModuleSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeSoundManager'
 */
class JSI_EXPORT NativeSoundManagerSpecJSI : public JavaTurboModule {
public:
  NativeSoundManagerSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeSourceCode'
 */
class JSI_EXPORT NativeSourceCodeSpecJSI : public JavaTurboModule {
public:
  NativeSourceCodeSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeStatusBarManagerAndroid'
 */
class JSI_EXPORT NativeStatusBarManagerAndroidSpecJSI : public JavaTurboModule {
public:
  NativeStatusBarManagerAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeTiming'
 */
class JSI_EXPORT NativeTimingSpecJSI : public JavaTurboModule {
public:
  NativeTimingSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeToastAndroid'
 */
class JSI_EXPORT NativeToastAndroidSpecJSI : public JavaTurboModule {
public:
  NativeToastAndroidSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeVibration'
 */
class JSI_EXPORT NativeVibrationSpecJSI : public JavaTurboModule {
public:
  NativeVibrationSpecJSI(const JavaTurboModule::InitParams &params);
};

/**
 * JNI C++ class for module 'NativeWebSocketModule'
 */
class JSI_EXPORT NativeWebSocketModuleSpecJSI : public JavaTurboModule {
public:
  NativeWebSocketModuleSpecJSI(const JavaTurboModule::InitParams &params);
};


JSI_EXPORT
std::shared_ptr<TurboModule> rncore_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params);

} // namespace facebook::react
